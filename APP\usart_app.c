#include "usart_app.h"


uint8_t uart_rx_dma_buffer[128];
uint8_t uart_dma_buffer[128];
volatile uint8_t uart_flag = 0;
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. 确认是目标串口 (USART1)
    if (huart->Instance == USART1)
    {
        // 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
        //    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
        HAL_UART_DMAStop(huart);

			// 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 放入到环形缓冲区
        rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);
			// 注意：这里使用了 Size，只放入实际接收到的数据
 
        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        //    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. **关键：重新启动下一次 DMA 空闲接收**
        //    必须再次调用，否则只会接收这一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        
        // 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
         __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}


/**
 * @brief  处理 DMA 接收到的 UART 数据
 * @param  None
 * @retval None
 */
void usart_task(void)
{
		uint16_t length;

	 length = rt_ringbuffer_data_len(&uart_ringbuffer);
	
		if (length == 0)
		return;


	//  拿出环形缓存区中的数据
	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);

    //    这里简单地打印出来，实际应用中会进行解析、执行命令等
    my_printf(&huart1,"DMA data: %s\n", uart_dma_buffer);
    //    (注意：如果数据不是字符串，需要用其他方式处理，比如按字节解析)
    
    //  清空"待处理货架"，为下次接收做准备
    memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}
