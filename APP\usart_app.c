#include "usart_app.h"


uint8_t uart_rx_dma_buffer[128];
uint8_t uart_dma_buffer[128];
volatile uint8_t uart_flag = 0;
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

// 调试计数器
volatile uint32_t debug_callback_count = 0;
volatile uint32_t debug_task_run_count = 0;
volatile uint32_t debug_data_received_count = 0;
volatile uint32_t debug_ringbuffer_put_count = 0;
volatile uint32_t debug_ringbuffer_get_count = 0;

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    debug_callback_count++;
    
    // 立即发送调试信息 - 确认回调函数被调用
    my_printf(&huart1, "[DEBUG] RxCallback #%lu: Size=%d\r\n", debug_callback_count, Size);
    
    // 1. 确认是目标串口 (USART1)
    if (huart->Instance == USART1)
    {
        my_printf(&huart1, "[DEBUG] USART1 confirmed\r\n");
        
        // 调试：显示接收到的原始数据
        my_printf(&huart1, "[DEBUG] Raw data: ");
        for(int i = 0; i < Size && i < 32; i++) {
            my_printf(&huart1, "%02X ", uart_rx_dma_buffer[i]);
        }
        my_printf(&huart1, "\r\n");
        
        // 调试：显示接收到的字符串数据
        if(Size < sizeof(uart_rx_dma_buffer)) {
            uart_rx_dma_buffer[Size] = '\0'; // 确保字符串结束
        }
        my_printf(&huart1, "[DEBUG] String data: '%s'\r\n", uart_rx_dma_buffer);
        
        // 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
        HAL_UART_DMAStop(huart);
        my_printf(&huart1, "[DEBUG] DMA stopped\r\n");

        // 调试：检查环形缓冲区状态（放入前）
        uint16_t space_before = rt_ringbuffer_space_len(&uart_ringbuffer);
        uint16_t data_before = rt_ringbuffer_data_len(&uart_ringbuffer);
        my_printf(&huart1, "[DEBUG] RingBuffer before: space=%d, data=%d\r\n", space_before, data_before);

        // 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 放入到环形缓冲区
        rt_size_t put_result = rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);
        debug_ringbuffer_put_count++;
        my_printf(&huart1, "[DEBUG] RingBuffer put: requested=%d, actual=%d\r\n", Size, put_result);
        
        // 调试：检查环形缓冲区状态（放入后）
        uint16_t space_after = rt_ringbuffer_space_len(&uart_ringbuffer);
        uint16_t data_after = rt_ringbuffer_data_len(&uart_ringbuffer);
        my_printf(&huart1, "[DEBUG] RingBuffer after: space=%d, data=%d\r\n", space_after, data_after);
 
        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));
        my_printf(&huart1, "[DEBUG] DMA buffer cleared\r\n");

        // 6. **关键：重新启动下一次 DMA 空闲接收**
        HAL_StatusTypeDef dma_status = HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        my_printf(&huart1, "[DEBUG] DMA restart status: %d\r\n", dma_status);
        
        // 7. 重新禁用半满中断
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
        my_printf(&huart1, "[DEBUG] Half-transfer interrupt disabled\r\n");
        
        debug_data_received_count++;
        my_printf(&huart1, "[DEBUG] Callback completed #%lu\r\n", debug_data_received_count);
    }
    else
    {
        my_printf(&huart1, "[DEBUG] Wrong UART instance: %p\r\n", huart->Instance);
    }
}


/**
 * @brief  处理 DMA 接收到的 UART 数据
 * @param  None
 * @retval None
 */
void usart_task(void)
{
    debug_task_run_count++;
    
    // 每1000次运行输出一次状态
    if(debug_task_run_count % 1000 == 0) {
        my_printf(&huart1, "[DEBUG] Task run #%lu\r\n", debug_task_run_count);
    }
    
    uint16_t length = rt_ringbuffer_data_len(&uart_ringbuffer);
    
    // 调试：显示环形缓冲区状态
    if(debug_task_run_count % 1000 == 0) {
        my_printf(&huart1, "[DEBUG] Task: RingBuffer length=%d\r\n", length);
    }
    
    if (length == 0)
        return;

    my_printf(&huart1, "[DEBUG] Task: Found data, length=%d\r\n", length);

    //  拿出环形缓存区中的数据
    rt_size_t get_result = rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);
    debug_ringbuffer_get_count++;
    my_printf(&huart1, "[DEBUG] Task: RingBuffer get: requested=%d, actual=%d\r\n", length, get_result);

    //    这里简单地打印出来，实际应用中会进行解析、执行命令等
    my_printf(&huart1, "DMA data: %s\n", uart_dma_buffer);
    //    (注意：如果数据不是字符串，需要用其他方式处理，比如按字节解析)
    
    //  清空"待处理货架"，为下次接收做准备
    memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
    
    my_printf(&huart1, "[DEBUG] Task: Data processed\r\n");
}
