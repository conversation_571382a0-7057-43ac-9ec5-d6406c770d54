--cpu Cortex-M3
"f103vet6\startup_stm32f103xe.o"
"f103vet6\main.o"
"f103vet6\gpio.o"
"f103vet6\dma.o"
"f103vet6\usart.o"
"f103vet6\stm32f1xx_it.o"
"f103vet6\stm32f1xx_hal_msp.o"
"f103vet6\stm32f1xx_hal_gpio_ex.o"
"f103vet6\stm32f1xx_hal_uart.o"
"f103vet6\stm32f1xx_hal.o"
"f103vet6\stm32f1xx_hal_rcc.o"
"f103vet6\stm32f1xx_hal_rcc_ex.o"
"f103vet6\stm32f1xx_hal_gpio.o"
"f103vet6\stm32f1xx_hal_dma.o"
"f103vet6\stm32f1xx_hal_cortex.o"
"f103vet6\stm32f1xx_hal_pwr.o"
"f103vet6\stm32f1xx_hal_flash.o"
"f103vet6\stm32f1xx_hal_flash_ex.o"
"f103vet6\stm32f1xx_hal_exti.o"
"f103vet6\system_stm32f1xx.o"
"f103vet6\scheduler.o"
"f103vet6\usart_app.o"
"f103vet6\ringbuffer.o"
--strict --scatter "f103vet6\f103vet6.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "f103vet6.map" -o f103vet6\f103vet6.axf