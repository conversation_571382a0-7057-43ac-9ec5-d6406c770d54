Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xe.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xe.o(.text) refers to startup_stm32f103xe.o(HEAP) for Heap_Mem
    startup_stm32f103xe.o(.text) refers to startup_stm32f103xe.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to usart_app.o(.bss) for ringbuffer_pool
    main.o(i.main) refers to usart_app.o(.bss) for uart_ringbuffer
    main.o(i.main) refers to usart.o(.bss) for huart1
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to usart_app.o(i.usart_task) for usart_task
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for .bss
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.usart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.usart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.usart_task) refers to usart_app.o(.bss) for .bss
    usart_app.o(i.usart_task) refers to usart.o(.bss) for huart1
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xe.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (64 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (184 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (316 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (128 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (988 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.data), (1 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).

229 unused section(s) (total 10441 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\usart_app.c                       0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer\ringbuffer.c 0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\usart_app.c                     0x00000000   Number         0  usart_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001a4   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x080001aa   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x080001b0   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x080001b6   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x080001bc   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x080001c2   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x080001c8   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x080001d2   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x080001d8   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080001de   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001e4   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001ea   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001f0   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001f6   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001fc   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x08000202   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000208   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x0800020e   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000218   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x0800021e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x08000224   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x0800022a   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000230   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000234   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000236   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x0800023c   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000248   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000252   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000254   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000256   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000258   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000258   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800025e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800025e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000262   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000262   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800026a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800026c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800026c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000270   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000278   Section       64  startup_stm32f103xe.o(.text)
    .text                                    0x080002b8   Section        0  vsnprintf.o(.text)
    .text                                    0x080002ec   Section        0  abort.o(.text)
    .text                                    0x08000302   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800038c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080003da   Section        0  heapauxi.o(.text)
    .text                                    0x080003e0   Section        0  sys_exit.o(.text)
    .text                                    0x080003ec   Section        0  _printf_pad.o(.text)
    .text                                    0x0800043a   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800045e   Section        0  _printf_str.o(.text)
    .text                                    0x080004b0   Section        0  _printf_dec.o(.text)
    .text                                    0x08000528   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000550   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000551   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000580   Section        0  _sputc.o(.text)
    .text                                    0x0800058a   Section        0  _snputc.o(.text)
    .text                                    0x0800059c   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000658   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080006d4   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080006d5   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000744   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000745   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080007d8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000960   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x0800096e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080009d2   Section        2  use_no_semi.o(.text)
    .text                                    0x080009d4   Section        0  indicate_semi.o(.text)
    .text                                    0x080009d4   Section      138  lludiv10.o(.text)
    .text                                    0x08000a5e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000b10   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000b13   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000f30   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x0800122c   Section        0  _printf_char.o(.text)
    .text                                    0x08001258   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001284   Section        0  _wcrtomb.o(.text)
    .text                                    0x080012c4   Section        0  defsig_exit.o(.text)
    .text                                    0x080012d0   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001300   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800134c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800135c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001364   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080013e4   Section        0  bigflt0.o(.text)
    .text                                    0x080014c8   Section        0  exit.o(.text)
    .text                                    0x080014da   Section        0  defsig_general.o(.text)
    .text                                    0x0800150c   Section        0  sys_wrch.o(.text)
    .text                                    0x0800151c   Section        8  libspace.o(.text)
    .text                                    0x08001524   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080015a4   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080015e2   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001628   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001688   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080019c0   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001a9c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001ac6   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001af0   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08001d34   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel5_IRQHandler               0x08001d38   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA_SetConfig                          0x08001d44   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001d45   Thumb Code    42  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08001d6e   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08001d70   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08001d74   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001dbc   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08001eec   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002134   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080021b0   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x08002220   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GetTick                            0x08002418   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002424   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002434   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002458   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002498   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080024d4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080024f0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002530   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002554   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002680   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080026a0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080026c0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800270c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08002a2c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08002a54   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08002aa0   Section        0  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002ae0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002b50   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002b54   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002dc0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002e24   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08002ed4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08002ed6   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08002ed8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002f78   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002f7a   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.MX_DMA_Init                            0x08002f7c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08002fa8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x08002fc4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08002ffc   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002ffe   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08003000   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08003002   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08003004   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003008   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003058   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x0800305a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x0800305b   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x0800306a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x0800306b   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080030b4   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080030b5   Thumb Code   134  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x0800313a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x0800313b   Thumb Code    30  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08003158   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003159   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x080031a6   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080031a7   Thumb Code    28  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080031c2   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080031c3   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08003284   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08003285   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x0800333c   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x080033cc   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080033cd   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08003440   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x0800344c   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x0800344e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08003476   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08003477   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x08003496   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x080034a4   Section        0  main.o(i.main)
    i.my_printf                              0x080034f8   Section        0  usart_app.o(i.my_printf)
    i.rt_ringbuffer_data_len                 0x0800352a   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x0800355a   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x080035ce   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x080035fe   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08003676   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    i.scheduler_init                         0x08003698   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x080036a4   Section        0  scheduler.o(i.scheduler_run)
    i.usart_task                             0x080036e0   Section        0  usart_app.o(i.usart_task)
    locale$$code                             0x08003728   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08003754   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$printf1                            0x08003780   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08003784   Section        4  printf2.o(x$fpl$printf2)
    .constdata                               0x08003788   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    x$fpl$usenofp                            0x08003788   Section        0  usenofp.o(x$fpl$usenofp)
    aPredivFactorTable                       0x08003788   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x0800378a   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800379a   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x080037aa   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x080037b4   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x080037b4   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080037bc   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x080037bc   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x080037d0   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x080037e4   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080037e4   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080037f5   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080037f5   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08003808   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800381c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800381c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08003858   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080038d0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080038d4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080038dc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080038e8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080038ea   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080038eb   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080038ec   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080038ec   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080038f0   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080038f8   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080039fc   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000010   Section       16  scheduler.o(.data)
    scheduler_task                           0x20000014   Data          12  scheduler.o(.data)
    .bss                                     0x20000020   Section      140  usart.o(.bss)
    .bss                                     0x200000ac   Section      268  usart_app.o(.bss)
    .bss                                     0x200001b8   Section      128  usart_app.o(.bss)
    .bss                                     0x20000238   Section       96  libspace.o(.bss)
    HEAP                                     0x20000298   Section      512  startup_stm32f103xe.o(HEAP)
    Heap_Mem                                 0x20000298   Data         512  startup_stm32f103xe.o(HEAP)
    STACK                                    0x20000498   Section     1024  startup_stm32f103xe.o(STACK)
    Stack_Mem                                0x20000498   Data        1024  startup_stm32f103xe.o(STACK)
    __initial_sp                             0x20000898   Data           0  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001a5   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x080001ab   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x080001b1   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x080001b7   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x080001bd   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x080001c3   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x080001c9   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x080001d3   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x080001d9   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080001df   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001e5   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001eb   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001f1   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001f7   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001fd   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x08000203   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000209   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x0800020f   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000219   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x0800021f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x08000225   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x0800022b   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000231   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000235   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000255   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000259   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000259   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800026b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000271   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000279   Thumb Code     8  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI0_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI15_10_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI1_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI2_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI4_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_UP_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_UP_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART3_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32f103xe.o(.text)
    __user_initial_stackheap                 0x08000295   Thumb Code     0  startup_stm32f103xe.o(.text)
    vsnprintf                                0x080002b9   Thumb Code    48  vsnprintf.o(.text)
    abort                                    0x080002ed   Thumb Code    22  abort.o(.text)
    __aeabi_memcpy                           0x08000303   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000303   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000369   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x0800038d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800038d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800038d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000391   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080003db   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080003dd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080003df   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x080003e1   Thumb Code     8  sys_exit.o(.text)
    _printf_pre_padding                      0x080003ed   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000419   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x0800043b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800044d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800045f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080004b1   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000529   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x0800055b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000581   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x0800058b   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x0800059d   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000659   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080006d5   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000717   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x0800072f   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000745   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x0800079b   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080007b7   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080007c3   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080007d9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __rt_SIGABRT                             0x08000961   Thumb Code    14  defsig_abrt_outer.o(.text)
    __aeabi_memcpy4                          0x0800096f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800096f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800096f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080009b7   Thumb Code     0  rt_memcpy_w.o(.text)
    __I$use$semihosting                      0x080009d3   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080009d3   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080009d5   Thumb Code     0  indicate_semi.o(.text)
    _ll_udiv10                               0x080009d5   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000a5f   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000b11   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000cc3   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08000f31   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x0800122d   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001241   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001251   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001259   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x0800126d   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x0800127d   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08001285   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x080012c5   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x080012d1   Thumb Code    14  defsig_abrt_inner.o(.text)
    __user_setup_stackheap                   0x08001301   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x0800134d   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x0800135d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001365   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080013e5   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x080014c9   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x080014db   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x0800150d   Thumb Code    14  sys_wrch.o(.text)
    __user_libspace                          0x0800151d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800151d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800151d   Thumb Code     0  libspace.o(.text)
    strcmp                                   0x08001525   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080015a5   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080015e3   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001629   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001689   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080019c1   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001a9d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001ac7   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001af1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001d35   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DMA1_Channel5_IRQHandler                 0x08001d39   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DebugMon_Handler                         0x08001d6f   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08001d71   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08001d75   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001dbd   Thumb Code   296  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001eed   Thumb Code   552  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002135   Thumb Code   112  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080021b1   Thumb Code   112  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08002221   Thumb Code   462  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GetTick                              0x08002419   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002425   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002435   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002459   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002499   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080024d5   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080024f1   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002531   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002555   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002681   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080026a1   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080026c1   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800270d   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002a2d   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002a55   Thumb Code    74  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002aa1   Thumb Code    52  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002ae1   Thumb Code   112  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002b51   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002b55   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002dc1   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002e25   Thumb Code   156  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08002ed5   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08002ed7   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08002ed9   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002f79   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002f7b   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x08002f7d   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08002fa9   Thumb Code    22  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08002fc5   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08002ffd   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002fff   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08003001   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08003003   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08003005   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003009   Thumb Code    80  main.o(i.SystemClock_Config)
    SystemInit                               0x08003059   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    UART_Start_Receive_DMA                   0x0800333d   Thumb Code   130  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08003441   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x0800344d   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x0800344f   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08003497   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x080034a5   Thumb Code    62  main.o(i.main)
    my_printf                                0x080034f9   Thumb Code    50  usart_app.o(i.my_printf)
    rt_ringbuffer_data_len                   0x0800352b   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x0800355b   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x080035cf   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080035ff   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x08003677   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    scheduler_init                           0x08003699   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x080036a5   Thumb Code    56  scheduler.o(i.scheduler_run)
    usart_task                               0x080036e1   Thumb Code    46  usart_app.o(i.usart_task)
    _get_lc_numeric                          0x08003729   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08003755   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _printf_fp_dec                           0x08003781   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08003785   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08003788   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800379a   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x080037aa   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x080038b0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080038d0   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080038f9   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx.o(.data)
    task_num                                 0x20000010   Data           1  scheduler.o(.data)
    huart1                                   0x20000020   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20000068   Data          68  usart.o(.bss)
    uart_rx_dma_buffer                       0x200000ac   Data         128  usart_app.o(.bss)
    uart_dma_buffer                          0x2000012c   Data         128  usart_app.o(.bss)
    uart_ringbuffer                          0x200001ac   Data          12  usart_app.o(.bss)
    ringbuffer_pool                          0x200001b8   Data         128  usart_app.o(.bss)
    __libspace_start                         0x20000238   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000298   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003a1c, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000039fc, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO            3    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000008   Code   RO         1967  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         2298    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         2300    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         2302    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         2055    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO         2044    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x080001aa   0x080001aa   0x00000006   Code   RO         2046    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO         2051    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO         2052    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x080001bc   0x080001bc   0x00000006   Code   RO         2053    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x080001c2   0x080001c2   0x00000006   Code   RO         2054    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x080001c8   0x080001c8   0x0000000a   Code   RO         2059    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x080001d2   0x080001d2   0x00000006   Code   RO         2048    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO         2049    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001de   0x080001de   0x00000006   Code   RO         2050    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO         2047    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001ea   0x080001ea   0x00000006   Code   RO         2045    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001f0   0x080001f0   0x00000006   Code   RO         2056    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001f6   0x080001f6   0x00000006   Code   RO         2057    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         2058    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x08000202   0x08000202   0x00000006   Code   RO         2063    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000208   0x08000208   0x00000006   Code   RO         2064    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x0800020e   0x0800020e   0x0000000a   Code   RO         2060    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000218   0x08000218   0x00000006   Code   RO         2042    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO         2043    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000224   0x08000224   0x00000006   Code   RO         2061    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         2062    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000230   0x08000230   0x00000004   Code   RO         2114    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000234   0x08000234   0x00000002   Code   RO         2171    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000236   0x08000236   0x00000000   Code   RO         2192    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO         2194    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO         2197    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO         2199    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO         2201    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000006   Code   RO         2202    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         2204    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x0000000c   Code   RO         2205    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         2206    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         2208    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x0000000a   Code   RO         2209    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2210    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2212    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2214    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2216    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2218    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2220    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2222    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2224    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2228    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2230    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2232    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2234    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000002   Code   RO         2235    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000254   0x08000254   0x00000002   Code   RO         2269    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2279    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2281    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2283    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2286    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2289    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2291    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2294    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000002   Code   RO         2295    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000258   0x08000258   0x00000000   Code   RO         1971    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000258   0x08000258   0x00000000   Code   RO         2083    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000006   Code   RO         2095    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2085    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800025e   0x0800025e   0x00000004   Code   RO         2086    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000000   Code   RO         2088    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000008   Code   RO         2089    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800026a   0x0800026a   0x00000002   Code   RO         2182    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         2237    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800026c   0x0800026c   0x00000004   Code   RO         2238    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000270   0x08000270   0x00000006   Code   RO         2239    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000276   0x08000276   0x00000002   PAD
    0x08000278   0x08000278   0x00000040   Code   RO            4    .text               startup_stm32f103xe.o
    0x080002b8   0x080002b8   0x00000034   Code   RO         1957    .text               c_w.l(vsnprintf.o)
    0x080002ec   0x080002ec   0x00000016   Code   RO         1959    .text               c_w.l(abort.o)
    0x08000302   0x08000302   0x0000008a   Code   RO         1961    .text               c_w.l(rt_memcpy_v6.o)
    0x0800038c   0x0800038c   0x0000004e   Code   RO         1963    .text               c_w.l(rt_memclr_w.o)
    0x080003da   0x080003da   0x00000006   Code   RO         1965    .text               c_w.l(heapauxi.o)
    0x080003e0   0x080003e0   0x0000000c   Code   RO         1969    .text               c_w.l(sys_exit.o)
    0x080003ec   0x080003ec   0x0000004e   Code   RO         1974    .text               c_w.l(_printf_pad.o)
    0x0800043a   0x0800043a   0x00000024   Code   RO         1976    .text               c_w.l(_printf_truncate.o)
    0x0800045e   0x0800045e   0x00000052   Code   RO         1978    .text               c_w.l(_printf_str.o)
    0x080004b0   0x080004b0   0x00000078   Code   RO         1980    .text               c_w.l(_printf_dec.o)
    0x08000528   0x08000528   0x00000028   Code   RO         1982    .text               c_w.l(_printf_charcount.o)
    0x08000550   0x08000550   0x00000030   Code   RO         1984    .text               c_w.l(_printf_char_common.o)
    0x08000580   0x08000580   0x0000000a   Code   RO         1986    .text               c_w.l(_sputc.o)
    0x0800058a   0x0800058a   0x00000010   Code   RO         1988    .text               c_w.l(_snputc.o)
    0x0800059a   0x0800059a   0x00000002   PAD
    0x0800059c   0x0800059c   0x000000bc   Code   RO         1990    .text               c_w.l(_printf_wctomb.o)
    0x08000658   0x08000658   0x0000007c   Code   RO         1993    .text               c_w.l(_printf_longlong_dec.o)
    0x080006d4   0x080006d4   0x00000070   Code   RO         1999    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000744   0x08000744   0x00000094   Code   RO         2019    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080007d8   0x080007d8   0x00000188   Code   RO         2039    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000960   0x08000960   0x0000000e   Code   RO         2065    .text               c_w.l(defsig_abrt_outer.o)
    0x0800096e   0x0800096e   0x00000064   Code   RO         2069    .text               c_w.l(rt_memcpy_w.o)
    0x080009d2   0x080009d2   0x00000002   Code   RO         2079    .text               c_w.l(use_no_semi.o)
    0x080009d4   0x080009d4   0x00000000   Code   RO         2081    .text               c_w.l(indicate_semi.o)
    0x080009d4   0x080009d4   0x0000008a   Code   RO         2099    .text               c_w.l(lludiv10.o)
    0x08000a5e   0x08000a5e   0x000000b2   Code   RO         2101    .text               c_w.l(_printf_intcommon.o)
    0x08000b10   0x08000b10   0x0000041e   Code   RO         2103    .text               c_w.l(_printf_fp_dec.o)
    0x08000f2e   0x08000f2e   0x00000002   PAD
    0x08000f30   0x08000f30   0x000002fc   Code   RO         2105    .text               c_w.l(_printf_fp_hex.o)
    0x0800122c   0x0800122c   0x0000002c   Code   RO         2110    .text               c_w.l(_printf_char.o)
    0x08001258   0x08001258   0x0000002c   Code   RO         2112    .text               c_w.l(_printf_wchar.o)
    0x08001284   0x08001284   0x00000040   Code   RO         2115    .text               c_w.l(_wcrtomb.o)
    0x080012c4   0x080012c4   0x0000000a   Code   RO         2117    .text               c_w.l(defsig_exit.o)
    0x080012ce   0x080012ce   0x00000002   PAD
    0x080012d0   0x080012d0   0x00000030   Code   RO         2119    .text               c_w.l(defsig_abrt_inner.o)
    0x08001300   0x08001300   0x0000004a   Code   RO         2121    .text               c_w.l(sys_stackheap_outer.o)
    0x0800134a   0x0800134a   0x00000002   PAD
    0x0800134c   0x0800134c   0x00000010   Code   RO         2123    .text               c_w.l(rt_ctype_table.o)
    0x0800135c   0x0800135c   0x00000008   Code   RO         2128    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001364   0x08001364   0x00000080   Code   RO         2130    .text               c_w.l(_printf_fp_infnan.o)
    0x080013e4   0x080013e4   0x000000e4   Code   RO         2132    .text               c_w.l(bigflt0.o)
    0x080014c8   0x080014c8   0x00000012   Code   RO         2160    .text               c_w.l(exit.o)
    0x080014da   0x080014da   0x00000032   Code   RO         2164    .text               c_w.l(defsig_general.o)
    0x0800150c   0x0800150c   0x0000000e   Code   RO         2177    .text               c_w.l(sys_wrch.o)
    0x0800151a   0x0800151a   0x00000002   PAD
    0x0800151c   0x0800151c   0x00000008   Code   RO         2179    .text               c_w.l(libspace.o)
    0x08001524   0x08001524   0x00000080   Code   RO         2189    .text               c_w.l(strcmpv7m.o)
    0x080015a4   0x080015a4   0x0000003e   Code   RO         2135    CL$$btod_d2e        c_w.l(btod.o)
    0x080015e2   0x080015e2   0x00000046   Code   RO         2137    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001628   0x08001628   0x00000060   Code   RO         2136    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001688   0x08001688   0x00000338   Code   RO         2145    CL$$btod_div_common  c_w.l(btod.o)
    0x080019c0   0x080019c0   0x000000dc   Code   RO         2142    CL$$btod_e2e        c_w.l(btod.o)
    0x08001a9c   0x08001a9c   0x0000002a   Code   RO         2139    CL$$btod_ediv       c_w.l(btod.o)
    0x08001ac6   0x08001ac6   0x0000002a   Code   RO         2138    CL$$btod_emul       c_w.l(btod.o)
    0x08001af0   0x08001af0   0x00000244   Code   RO         2144    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001d34   0x08001d34   0x00000002   Code   RO          258    i.BusFault_Handler  stm32f1xx_it.o
    0x08001d36   0x08001d36   0x00000002   PAD
    0x08001d38   0x08001d38   0x0000000c   Code   RO          259    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x08001d44   0x08001d44   0x0000002a   Code   RO         1149    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x08001d6e   0x08001d6e   0x00000002   Code   RO          260    i.DebugMon_Handler  stm32f1xx_it.o
    0x08001d70   0x08001d70   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08001d74   0x08001d74   0x00000046   Code   RO         1150    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08001dba   0x08001dba   0x00000002   PAD
    0x08001dbc   0x08001dbc   0x00000130   Code   RO         1151    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08001eec   0x08001eec   0x00000248   Code   RO         1155    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08002134   0x08002134   0x0000007c   Code   RO         1156    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x080021b0   0x080021b0   0x00000070   Code   RO         1160    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08002220   0x08002220   0x000001f8   Code   RO         1086    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08002418   0x08002418   0x0000000c   Code   RO          780    i.HAL_GetTick       stm32f1xx_hal.o
    0x08002424   0x08002424   0x00000010   Code   RO          786    i.HAL_IncTick       stm32f1xx_hal.o
    0x08002434   0x08002434   0x00000024   Code   RO          787    i.HAL_Init          stm32f1xx_hal.o
    0x08002458   0x08002458   0x00000040   Code   RO          788    i.HAL_InitTick      stm32f1xx_hal.o
    0x08002498   0x08002498   0x0000003c   Code   RO          346    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x080024d4   0x080024d4   0x0000001a   Code   RO         1246    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x080024ee   0x080024ee   0x00000002   PAD
    0x080024f0   0x080024f0   0x00000040   Code   RO         1252    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002530   0x08002530   0x00000024   Code   RO         1253    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08002554   0x08002554   0x0000012c   Code   RO          944    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08002680   0x08002680   0x00000020   Code   RO          951    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x080026a0   0x080026a0   0x00000020   Code   RO          952    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080026c0   0x080026c0   0x0000004c   Code   RO          953    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x0800270c   0x0800270c   0x00000320   Code   RO          956    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08002a2c   0x08002a2c   0x00000028   Code   RO         1257    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08002a54   0x08002a54   0x0000004a   Code   RO          416    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f1xx_hal_uart.o
    0x08002a9e   0x08002a9e   0x00000002   PAD
    0x08002aa0   0x08002aa0   0x00000040   Code   RO         1821    i.HAL_UARTEx_RxEventCallback  usart_app.o
    0x08002ae0   0x08002ae0   0x00000070   Code   RO          430    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x08002b50   0x08002b50   0x00000002   Code   RO          432    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x08002b52   0x08002b52   0x00000002   PAD
    0x08002b54   0x08002b54   0x0000026c   Code   RO          435    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002dc0   0x08002dc0   0x00000064   Code   RO          436    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x08002e24   0x08002e24   0x000000b0   Code   RO          217    i.HAL_UART_MspInit  usart.o
    0x08002ed4   0x08002ed4   0x00000002   Code   RO          442    i.HAL_UART_RxCpltCallback  stm32f1xx_hal_uart.o
    0x08002ed6   0x08002ed6   0x00000002   Code   RO          443    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08002ed8   0x08002ed8   0x000000a0   Code   RO          444    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08002f78   0x08002f78   0x00000002   Code   RO          447    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x08002f7a   0x08002f7a   0x00000002   Code   RO          261    i.HardFault_Handler  stm32f1xx_it.o
    0x08002f7c   0x08002f7c   0x0000002c   Code   RO          192    i.MX_DMA_Init       dma.o
    0x08002fa8   0x08002fa8   0x0000001c   Code   RO          168    i.MX_GPIO_Init      gpio.o
    0x08002fc4   0x08002fc4   0x00000038   Code   RO          218    i.MX_USART1_UART_Init  usart.o
    0x08002ffc   0x08002ffc   0x00000002   Code   RO          262    i.MemManage_Handler  stm32f1xx_it.o
    0x08002ffe   0x08002ffe   0x00000002   Code   RO          263    i.NMI_Handler       stm32f1xx_it.o
    0x08003000   0x08003000   0x00000002   Code   RO          264    i.PendSV_Handler    stm32f1xx_it.o
    0x08003002   0x08003002   0x00000002   Code   RO          265    i.SVC_Handler       stm32f1xx_it.o
    0x08003004   0x08003004   0x00000004   Code   RO          266    i.SysTick_Handler   stm32f1xx_it.o
    0x08003008   0x08003008   0x00000050   Code   RO           14    i.SystemClock_Config  main.o
    0x08003058   0x08003058   0x00000002   Code   RO         1747    i.SystemInit        system_stm32f1xx.o
    0x0800305a   0x0800305a   0x00000010   Code   RO          449    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x0800306a   0x0800306a   0x0000004a   Code   RO          450    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x080030b4   0x080030b4   0x00000086   Code   RO          451    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x0800313a   0x0800313a   0x0000001e   Code   RO          453    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x08003158   0x08003158   0x0000004e   Code   RO          459    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x080031a6   0x080031a6   0x0000001c   Code   RO          460    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x080031c2   0x080031c2   0x000000c2   Code   RO          461    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08003284   0x08003284   0x000000b8   Code   RO          462    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x0800333c   0x0800333c   0x00000090   Code   RO          463    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x080033cc   0x080033cc   0x00000072   Code   RO          465    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x0800343e   0x0800343e   0x00000002   PAD
    0x08003440   0x08003440   0x0000000c   Code   RO          267    i.USART1_IRQHandler  stm32f1xx_it.o
    0x0800344c   0x0800344c   0x00000002   Code   RO          268    i.UsageFault_Handler  stm32f1xx_it.o
    0x0800344e   0x0800344e   0x00000028   Code   RO         2175    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08003476   0x08003476   0x00000020   Code   RO         1259    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08003496   0x08003496   0x0000000e   Code   RO         2032    i._is_digit         c_w.l(__printf_wp.o)
    0x080034a4   0x080034a4   0x00000054   Code   RO           15    i.main              main.o
    0x080034f8   0x080034f8   0x00000032   Code   RO         1822    i.my_printf         usart_app.o
    0x0800352a   0x0800352a   0x00000030   Code   RO         1870    i.rt_ringbuffer_data_len  ringbuffer.o
    0x0800355a   0x0800355a   0x00000074   Code   RO         1871    i.rt_ringbuffer_get  ringbuffer.o
    0x080035ce   0x080035ce   0x00000030   Code   RO         1873    i.rt_ringbuffer_init  ringbuffer.o
    0x080035fe   0x080035fe   0x00000078   Code   RO         1875    i.rt_ringbuffer_put  ringbuffer.o
    0x08003676   0x08003676   0x00000020   Code   RO         1880    i.rt_ringbuffer_status  ringbuffer.o
    0x08003696   0x08003696   0x00000002   PAD
    0x08003698   0x08003698   0x0000000c   Code   RO         1784    i.scheduler_init    scheduler.o
    0x080036a4   0x080036a4   0x0000003c   Code   RO         1785    i.scheduler_run     scheduler.o
    0x080036e0   0x080036e0   0x00000048   Code   RO         1823    i.usart_task        usart_app.o
    0x08003728   0x08003728   0x0000002c   Code   RO         2158    locale$$code        c_w.l(lc_numeric_c.o)
    0x08003754   0x08003754   0x0000002c   Code   RO         2185    locale$$code        c_w.l(lc_ctype_c.o)
    0x08003780   0x08003780   0x00000004   Code   RO         2071    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08003784   0x08003784   0x00000004   Code   RO         2073    x$fpl$printf2       fz_ws.l(printf2.o)
    0x08003788   0x08003788   0x00000000   Code   RO         2174    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003788   0x08003788   0x00000012   Data   RO          957    .constdata          stm32f1xx_hal_rcc.o
    0x0800379a   0x0800379a   0x00000010   Data   RO         1748    .constdata          system_stm32f1xx.o
    0x080037aa   0x080037aa   0x00000008   Data   RO         1749    .constdata          system_stm32f1xx.o
    0x080037b2   0x080037b2   0x00000002   PAD
    0x080037b4   0x080037b4   0x00000008   Data   RO         1991    .constdata          c_w.l(_printf_wctomb.o)
    0x080037bc   0x080037bc   0x00000028   Data   RO         2020    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080037e4   0x080037e4   0x00000011   Data   RO         2040    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080037f5   0x080037f5   0x00000026   Data   RO         2106    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800381b   0x0800381b   0x00000001   PAD
    0x0800381c   0x0800381c   0x00000094   Data   RO         2133    .constdata          c_w.l(bigflt0.o)
    0x080038b0   0x080038b0   0x00000020   Data   RO         2296    Region$$Table       anon$$obj.o
    0x080038d0   0x080038d0   0x0000001c   Data   RO         2157    locale$$data        c_w.l(lc_numeric_c.o)
    0x080038ec   0x080038ec   0x00000110   Data   RO         2184    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080039fc, Size: 0x00000898, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080039fc   0x0000000c   Data   RW          794    .data               stm32f1xx_hal.o
    0x2000000c   0x08003a08   0x00000004   Data   RW         1750    .data               system_stm32f1xx.o
    0x20000010   0x08003a0c   0x00000010   Data   RW         1786    .data               scheduler.o
    0x20000020        -       0x0000008c   Zero   RW          219    .bss                usart.o
    0x200000ac        -       0x0000010c   Zero   RW         1824    .bss                usart_app.o
    0x200001b8        -       0x00000080   Zero   RW         1825    .bss                usart_app.o
    0x20000238        -       0x00000060   Zero   RW         2180    .bss                c_w.l(libspace.o)
    0x20000298        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xe.o
    0x20000498        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          4          0          0          0        734   dma.o
        28          6          0          0          0        719   gpio.o
       168         22          0          0          0     456873   main.o
       364          0          0          0          0       6214   ringbuffer.o
        72          8          0         16          0       1568   scheduler.o
        64         26        304          0       1536        800   startup_stm32f103xe.o
       128         24          0         12          0       5821   stm32f1xx_hal.o
       198         14          0          0          0      28867   stm32f1xx_hal_cortex.o
      1236         52          0          0          0       5034   stm32f1xx_hal_dma.o
       504         42          0          0          0       2256   stm32f1xx_hal_gpio.o
        60          8          0          0          0        850   stm32f1xx_hal_msp.o
      1240         84         18          0          0       5040   stm32f1xx_hal_rcc.o
      2070         24          0          0          0      14748   stm32f1xx_hal_uart.o
        44         12          0          0          0       4617   stm32f1xx_it.o
         2          0         24          4          0       1083   system_stm32f1xx.o
       232         28          0          0        140       1737   usart.o
       186         38          0          0        396       3057   usart_app.o

    ----------------------------------------------------------------------
      6654        <USER>        <GROUP>         32       2072     540018   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      7258        <USER>        <GROUP>          0         96       4864   Library Totals
        14          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7196        300        551          0         96       4660   c_w.l
         8          0          0          0          0        136   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      7258        <USER>        <GROUP>          0         96       4864   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13912        692        932         32       2168     538742   Grand Totals
     13912        692        932         32       2168     538742   ELF Image Totals
     13912        692        932         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                14844 (  14.50kB)
    Total RW  Size (RW Data + ZI Data)              2200 (   2.15kB)
    Total ROM Size (Code + RO Data + RW Data)      14876 (  14.53kB)

==============================================================================

